// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class InvoiceItemAdapter extends TypeAdapter<InvoiceItem> {
  @override
  final int typeId = 2;

  @override
  InvoiceItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return InvoiceItem(
      product: fields[0] as Product,
      quantity: fields[1] as int,
      totalPrice: fields[2] as double,
      returnedQuantity: fields[3] == null ? 0 : fields[3] as int,
      returnedAmount: fields[4] == null ? 0.0 : fields[4] as double,
    );
  }

  @override
  void write(BinaryWriter writer, InvoiceItem obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.product)
      ..writeByte(1)
      ..write(obj.quantity)
      ..writeByte(2)
      ..write(obj.totalPrice)
      ..writeByte(3)
      ..write(obj.returnedQuantity)
      ..writeByte(4)
      ..write(obj.returnedAmount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InvoiceItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InvoiceAdapter extends TypeAdapter<Invoice> {
  @override
  final int typeId = 3;

  @override
  Invoice read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Invoice(
      id: fields[0] as String,
      customer: fields[1] as Customer,
      items: (fields[2] as List).cast<InvoiceItem>(),
      date: fields[3] as DateTime,
      totalAmount: fields[4] as double,
      paidAmount: fields[5] as double?,
      isPaid: fields[6] as bool,
      totalReturnedAmount: fields[7] == null ? 0.0 : fields[7] as double,
      lastReturnDate: fields[8] as DateTime?,
      hasReturns: fields[9] == null ? false : fields[9] as bool,
      collectedAmount: fields[10] == null ? 0.0 : fields[10] as double,
    );
  }

  @override
  void write(BinaryWriter writer, Invoice obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.customer)
      ..writeByte(2)
      ..write(obj.items)
      ..writeByte(3)
      ..write(obj.date)
      ..writeByte(4)
      ..write(obj.totalAmount)
      ..writeByte(5)
      ..write(obj.paidAmount)
      ..writeByte(6)
      ..write(obj.isPaid)
      ..writeByte(7)
      ..write(obj.totalReturnedAmount)
      ..writeByte(8)
      ..write(obj.lastReturnDate)
      ..writeByte(9)
      ..write(obj.hasReturns)
      ..writeByte(10)
      ..write(obj.collectedAmount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InvoiceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
