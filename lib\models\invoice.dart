import 'package:hive/hive.dart';
import 'customer.dart';
import 'product.dart';

part 'invoice.g.dart';

@HiveType(typeId: 2)
class InvoiceItem extends HiveObject {
  @HiveField(0)
  Product product;

  @HiveField(1)
  int quantity;

  @HiveField(2)
  double totalPrice;

  @HiveField(3, defaultValue: 0)
  int returnedQuantity;

  @HiveField(4, defaultValue: 0.0)
  double returnedAmount;

  InvoiceItem({
    required this.product,
    required this.quantity,
    required this.totalPrice,
    this.returnedQuantity = 0,
    this.returnedAmount = 0.0,
  });

  // Helper getters
  int get netQuantity => quantity - returnedQuantity;
  double get netAmount => totalPrice - returnedAmount;
  bool get isFullyReturned => returnedQuantity >= quantity;
  bool get isPartiallyReturned =>
      returnedQuantity > 0 && returnedQuantity < quantity;
}

@HiveType(typeId: 3)
class Invoice extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  Customer customer;

  @HiveField(2)
  List<InvoiceItem> items;

  @HiveField(3)
  DateTime date;

  @HiveField(4)
  double totalAmount;

  @HiveField(5)
  double? paidAmount;

  @HiveField(6)
  bool isPaid;

  @HiveField(7, defaultValue: 0.0)
  double totalReturnedAmount;

  @HiveField(8)
  DateTime? lastReturnDate;

  @HiveField(9, defaultValue: false)
  bool hasReturns;

  @HiveField(10, defaultValue: 0.0)
  double collectedAmount;

  Invoice({
    required this.id,
    required this.customer,
    required this.items,
    required this.date,
    required this.totalAmount,
    this.paidAmount,
    this.isPaid = false,
    this.totalReturnedAmount = 0.0,
    this.lastReturnDate,
    this.hasReturns = false,
    this.collectedAmount = 0.0,
  });

  // Helper getters for return calculations
  double get originalAmount => totalAmount + totalReturnedAmount;
  bool get isFullyReturned => totalReturnedAmount >= originalAmount;
  bool get isPartiallyReturned =>
      totalReturnedAmount > 0 && totalReturnedAmount < originalAmount;

  static const String institutionName =
      'Mohammed Ali Bakri Al-Zubaidi Veterinary Institution';
  static const String taxNumber = ''; // Removed as requested
  static const String commercialRegistrationNumber = '٤٠٣١٣١٩٥٧٢';
}
