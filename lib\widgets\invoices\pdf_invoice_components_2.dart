import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../models/invoice.dart';
import '../../models/return.dart';

// Items table header component
pw.TableRow buildItemsTableHeader(pw.Font ttf) {
  return pw.TableRow(
    decoration: pw.BoxDecoration(
      color: PdfColor.fromHex('#D6B36A'), // Dark gray
    ),
    children: [
      pw.Padding(
        padding: const pw.EdgeInsets.all(10),
        child: pw.Text(
          '#',
          style: pw.TextStyle(
            font: ttf,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.white,
            fontSize: 10,
          ),
          textAlign: pw.TextAlign.center,
        ),
      ),
      pw.Padding(
        padding: const pw.EdgeInsets.all(10),
        child: pw.Text(
          'المنتج', // Product
          style: pw.TextStyle(
            font: ttf,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.white,
            fontSize: 10,
          ),
          textAlign: pw.TextAlign.center,
        ),
      ),
      pw.Padding(
        padding: const pw.EdgeInsets.all(10),
        child: pw.Text(
          'الكمية للوحدة', // Quantity per Unit
          style: pw.TextStyle(
            font: ttf,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.white,
            fontSize: 10,
          ),
        ),
      ),
      pw.Padding(
        padding: const pw.EdgeInsets.all(10),
        child: pw.Text(
          'السعر', // Price (Arabic)
          style: pw.TextStyle(
            font: ttf,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.white,
            fontSize: 10,
          ),
        ),
      ),
      pw.Padding(
        padding: const pw.EdgeInsets.all(10),
        child: pw.Text(
          'إجمالي', // Total (Arabic)
          style: pw.TextStyle(
            font: ttf,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.white,
            fontSize: 10,
          ),
          textAlign: pw.TextAlign.center,
        ),
      ),
    ],
  );
}

// Items table row component
pw.TableRow buildItemsTableRow(int index, InvoiceItem item, pw.Font ttf) {
  // Get product category and price
  final productCategory = item.product.category;
  final pricePerUnit = item.product.pricePerUnit;

  return pw.TableRow(
    decoration: pw.BoxDecoration(
      color: index % 2 == 0
          ? PdfColors.white
          : PdfColor.fromHex('#F5F5F5'), // Light gray for alternating rows
    ),
    children: [
      pw.Padding(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Text(
          (index + 1).toString(), // Item number
          style: pw.TextStyle(
            font: ttf,
            fontSize: 9,
            color: PdfColor.fromHex('#4C585B'), // Dark gray
          ),
          textAlign: pw.TextAlign.center,
        ),
      ),
      pw.Padding(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              productCategory, // Product name
              style: pw.TextStyle(
                font: ttf,
                fontSize: 9,
                color: PdfColor.fromHex('#4C585B'), // Dark gray
              ),
            ),
            // Show return information if item has returns
            if (item.returnedQuantity > 0) ...[
              pw.SizedBox(height: 2),
              pw.Text(
                'مرتجع: ${item.returnedQuantity} حبّة',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 8,
                  color:
                      PdfColor.fromHex('#FF6B35'), // Orange color for returns
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
      ),
      pw.Padding(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Text(
              '${item.quantity} حبّة', // Original quantity
              style: pw.TextStyle(
                font: ttf,
                fontSize: 9,
                color: PdfColor.fromHex('#4C585B'), // Dark gray
              ),
              textAlign: pw.TextAlign.center,
            ),
            // Show net quantity if there are returns
            if (item.returnedQuantity > 0) ...[
              pw.SizedBox(height: 2),
              pw.Text(
                'صافي: ${item.netQuantity} حبّة',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 8,
                  color: PdfColor.fromHex('#2E7D32'), // Green color for net
                  fontWeight: pw.FontWeight.bold,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ],
          ],
        ),
      ),
      pw.Padding(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Text(
          pricePerUnit.toStringAsFixed(2), // Price per unit
          style: pw.TextStyle(
            font: ttf,
            fontSize: 9,
            color: PdfColor.fromHex('#4C585B'), // Dark gray
          ),
          textAlign: pw.TextAlign.left,
        ),
      ),
      pw.Padding(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            pw.Text(
              item.totalPrice.toStringAsFixed(2), // Original total price
              style: pw.TextStyle(
                font: ttf,
                fontSize: 9,
                color: item.returnedQuantity > 0
                    ? PdfColor.fromHex('#9E9E9E') // Gray if there are returns
                    : PdfColor.fromHex('#4C585B'), // Dark gray
                decoration: item.returnedQuantity > 0
                    ? pw.TextDecoration.lineThrough
                    : pw.TextDecoration.none,
              ),
              textAlign: pw.TextAlign.left,
            ),
            // Show net amount if there are returns
            if (item.returnedQuantity > 0) ...[
              pw.SizedBox(height: 2),
              pw.Text(
                item.netAmount.toStringAsFixed(2), // Net amount after returns
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 9,
                  color: PdfColor.fromHex('#2E7D32'), // Green color for net
                  fontWeight: pw.FontWeight.bold,
                ),
                textAlign: pw.TextAlign.left,
              ),
            ],
          ],
        ),
      ),
    ],
  );
}

// Items table component
pw.Widget buildItemsTable(Invoice invoice, pw.Font ttf) {
  return pw.Container(
    decoration: pw.BoxDecoration(
      border:
          pw.Border.all(color: PdfColor.fromHex('#4C585B')), // Dark gray border
      borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
    ),
    child: pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(0.5), // #
        1: const pw.FlexColumnWidth(3), // Product
        2: const pw.FlexColumnWidth(1.5), // Quantity/Unit
        3: const pw.FlexColumnWidth(2), // Price
        4: const pw.FlexColumnWidth(2), // Total
      },
      border: pw.TableBorder.symmetric(
        inside: pw.BorderSide(
            color: PdfColor.fromHex('#4C585B')), // Dark gray border
      ),
      tableWidth: pw.TableWidth.max,
      defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
      children: [
        // Table header
        buildItemsTableHeader(ttf),

        // Table rows - handle empty items list
        if (invoice.items.isNotEmpty)
          ...invoice.items.asMap().entries.map(
                (entry) => buildItemsTableRow(entry.key, entry.value, ttf),
              )
        else
          // Show an empty row if there are no items
          pw.TableRow(
            children: List.generate(
              5,
              (index) => pw.Padding(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  index == 1
                      ? 'لا توجد منتجات'
                      : '', // "No products" in the product column
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 9,
                    color: PdfColor.fromHex('#4C585B'),
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
          ),
      ],
    ),
  );
}

// Return information box component
pw.Widget buildReturnInfoBox(Invoice invoice, pw.Font ttf,
    {List<Return>? returnRecords}) {
  if (!invoice.hasReturns) {
    return pw.SizedBox.shrink(); // Don't show if no returns
  }

  return pw.Container(
    margin: const pw.EdgeInsets.only(bottom: 15),
    decoration: pw.BoxDecoration(
      color: PdfColor.fromHex('#FFF3E0'), // Light orange background
      border: pw.Border.all(
        color: PdfColor.fromHex('#FF6B35'), // Orange border
        width: 1.5,
      ),
      borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
    ),
    child: pw.Column(
      children: [
        // Header
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex('#FF6B35'), // Orange header
            borderRadius: const pw.BorderRadius.only(
              topLeft: pw.Radius.circular(8),
              topRight: pw.Radius.circular(8),
            ),
          ),
          child: pw.Text(
            'معلومات الإرجاع', // Return Information
            style: pw.TextStyle(
              font: ttf,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.white,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),

        // Content
        pw.Padding(
          padding: const pw.EdgeInsets.all(12),
          child: pw.Column(
            children: [
              // Return records details
              if (returnRecords != null && returnRecords.isNotEmpty) ...[
                pw.Text(
                  'تفاصيل الإرجاع:',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 11,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColor.fromHex('#4C585B'),
                  ),
                ),
                pw.SizedBox(height: 8),

                // List return records
                ...returnRecords.map((returnRecord) => pw.Container(
                      margin: const pw.EdgeInsets.only(bottom: 8),
                      padding: const pw.EdgeInsets.all(8),
                      decoration: pw.BoxDecoration(
                        color: PdfColor.fromHex('#FFF8F0'),
                        borderRadius:
                            const pw.BorderRadius.all(pw.Radius.circular(4)),
                        border: pw.Border.all(
                          color: PdfColor.fromHex('#FFB366'),
                          width: 0.5,
                        ),
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // Return date and amount
                          pw.Row(
                            mainAxisAlignment:
                                pw.MainAxisAlignment.spaceBetween,
                            children: [
                              pw.Text(
                                '${returnRecord.returnDate.day}/${returnRecord.returnDate.month}/${returnRecord.returnDate.year}',
                                style: pw.TextStyle(
                                  font: ttf,
                                  fontSize: 9,
                                  fontWeight: pw.FontWeight.bold,
                                  color: PdfColor.fromHex('#4C585B'),
                                ),
                              ),
                              pw.Text(
                                'SAR ${returnRecord.totalReturnAmount.toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                  font: ttf,
                                  fontSize: 9,
                                  fontWeight: pw.FontWeight.bold,
                                  color: PdfColor.fromHex('#FF6B35'),
                                ),
                              ),
                            ],
                          ),
                          pw.SizedBox(height: 4),

                          // Return items
                          ...returnRecord.items.map((item) => pw.Padding(
                                padding: const pw.EdgeInsets.only(
                                    left: 8, bottom: 2),
                                child: pw.Text(
                                  '• ${item.productCategory}: ${item.returnedQuantity} حبّة',
                                  style: pw.TextStyle(
                                    font: ttf,
                                    fontSize: 8,
                                    color: PdfColor.fromHex('#666666'),
                                  ),
                                ),
                              )),
                        ],
                      ),
                    )),

                pw.SizedBox(height: 8),
              ],

              // Return date (fallback if no detailed records)
              if ((returnRecords == null || returnRecords.isEmpty) &&
                  invoice.lastReturnDate != null) ...[
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'تاريخ آخر إرجاع:',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 10,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColor.fromHex('#4C585B'),
                      ),
                    ),
                    pw.Text(
                      '${invoice.lastReturnDate!.day}/${invoice.lastReturnDate!.month}/${invoice.lastReturnDate!.year}',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 10,
                        color: PdfColor.fromHex('#4C585B'),
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 8),
              ],

              // Original amount
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'المبلغ الأصلي:',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 10,
                      color: PdfColor.fromHex('#4C585B'),
                    ),
                  ),
                  pw.Text(
                    'SAR ${invoice.originalAmount.toStringAsFixed(2)}',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 10,
                      color: PdfColor.fromHex('#9E9E9E'),
                      decoration: pw.TextDecoration.lineThrough,
                    ),
                  ),
                ],
              ),
              pw.SizedBox(height: 5),

              // Returned amount
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'المبلغ المرتجع:',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColor.fromHex('#FF6B35'),
                    ),
                  ),
                  pw.Text(
                    '- SAR ${invoice.totalReturnedAmount.toStringAsFixed(2)}',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColor.fromHex('#FF6B35'),
                    ),
                  ),
                ],
              ),
              pw.SizedBox(height: 8),

              // Divider
              pw.Container(
                height: 1,
                color: PdfColor.fromHex('#FF6B35'),
                margin: const pw.EdgeInsets.symmetric(vertical: 5),
              ),

              // Net amount
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'المبلغ الصافي:',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColor.fromHex('#2E7D32'),
                    ),
                  ),
                  pw.Text(
                    'SAR ${invoice.totalAmount.toStringAsFixed(2)}',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColor.fromHex('#2E7D32'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

// Summary section component
pw.Widget buildSummarySection(
    double subtotal, double totalWithVat, double? paidAmount, pw.Font ttf,
    {double? collectedAmount}) {
  // Calculate remaining balance
  final paid = paidAmount ?? 0.0;
  final collected = collectedAmount ?? 0.0;

  return pw.Column(
    children: [
      // Add divider before summary section
      pw.Divider(
        color: PdfColor.fromHex('#4C585B'), // Dark gray
        thickness: 1,
        height: 20,
      ),

      pw.Align(
        alignment: pw.Alignment.topLeft,
        child: pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          width: 250, // تحديد عرض المساحة المخصصة
          child: pw.Column(
            crossAxisAlignment:
                pw.CrossAxisAlignment.start, // محاذاة للجانب الأيسر
            children: [
              pw.Container(
                width: double.infinity, // لضمان امتداد الصف لكامل العرض المحدد
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'سعر الفاتورة',
                      style: pw.TextStyle(font: ttf, fontSize: 12),
                    ),
                    pw.Text(
                      'SAR ${subtotal.toStringAsFixed(2)}',
                      style: pw.TextStyle(font: ttf, fontSize: 12),
                    ),
                  ],
                ),
              ),
              pw.SizedBox(height: 4), // مساحة بين الصفوف
              pw.Container(
                width: double.infinity,
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'المبلغ المحصل',
                      style: pw.TextStyle(font: ttf, fontSize: 12),
                    ),
                    pw.Text(
                      //!ده هنه المبلغ المحصل الي ثبته عند انشاء الفاتوره "collected"
                      'SAR ${collected.toStringAsFixed(2)}',
                      style: pw.TextStyle(font: ttf, fontSize: 12),
                    ),
                  ],
                ),
              ),
              pw.SizedBox(height: 8),
              // صف الإجمالي الذهبي
              pw.Container(
                width: double.infinity,
                color: PdfColor.fromHex('#D6B36A'),
                padding:
                    const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'الإجمالي:',
                      style: pw.TextStyle(
                        font: ttf,
                        fontWeight: pw.FontWeight.bold,
                        fontSize: 16,
                        color: PdfColors.black,
                      ),
                    ),
                    pw.Text(
                      //!سند الدفع وهو المجموع الي في حساب العميل "paid"

                      'SAR ${paid.toStringAsFixed(2)}',
                      style: pw.TextStyle(
                        font: ttf,
                        fontWeight: pw.FontWeight.bold,
                        fontSize: 18,
                        color: PdfColors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      pw.SizedBox(height: 10),
    ],
  );
}

// Footer component
pw.Widget buildFooter(
  pw.Font ttf,
  qrCode,
) {
  return pw.Container(
    child: pw.Column(
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              children: [
                pw.Text(
                  'شكرا لكم...',
                  style: pw.TextStyle(
                    font: ttf,
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 27,
                    color: PdfColor.fromHex('#125158'),
                  ),
                ),
                pw.SizedBox(height: 10),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    // Contact info

                    pw.Container(
                      width: 30,
                      height: 60,
                      color: PdfColor.fromHex('#125158'),
                      child: pw.Column(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                        children: [
                          pw.Text(
                            'هاتف',
                            style: pw.TextStyle(
                              fontSize: 9,
                              color: PdfColor.fromHex('#ffffff'),
                            ),
                          ),
                          pw.Text('هاتف',
                              style: pw.TextStyle(
                                fontSize: 9,
                                color: PdfColor.fromHex('#ffffff'),
                              )),
                          pw.Text(
                            'بريد',
                            style: pw.TextStyle(
                              fontSize: 9,
                              color: PdfColor.fromHex('#ffffff'),
                            ),
                          ),
                        ],
                      ),
                    ),
                    pw.SizedBox(width: 8),

                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          '0537545548',
                          style: pw.TextStyle(
                            fontSize: 10,
                            color: PdfColor.fromHex('#125158'),
                          ),
                        ),
                        pw.Text(
                          '0574901901',
                          style: pw.TextStyle(
                            fontSize: 10,
                            color: PdfColor.fromHex('#125158'),
                          ),
                        ),
                        pw.Text(
                          '',
                          style: pw.TextStyle(
                            fontSize: 10,
                            color: PdfColor.fromHex('#125158'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            pw.SizedBox(
              height: 90,
              width: 90,
              child: pw.Image(qrCode, fit: pw.BoxFit.contain),
            ),
          ],
        ),
        pw.Text('االمملكه العربيه السعوديه / جده',
            style: pw.TextStyle(
              font: ttf,
              fontWeight: pw.FontWeight.bold,
              fontSize: 11,
              color: PdfColor.fromHex('#125158'),
            )),
        pw.SizedBox(height: 10),
        pw.Container(
          width: double.infinity,
          height: 15,
          color: PdfColor.fromHex('#125158'), // Dark gray
        ),
      ],
    ),
  );
}

// Page number indicator
