import 'app_localizations.dart';

class AppLocalizationsEn extends AppLocalizations {
  // General
  @override
  String get appTitle => 'Al-Zubaidi Veterinary';

  // Navigation
  @override
  String get productsTab => 'Products';
  @override
  String get accountsTab => 'Accounts';
  @override
  String get invoicesTab => 'Invoices';
  @override
  String get cashboxTab => 'Cashbox';

  // Products Page
  @override
  String get lowStock => 'Almost Out of Stock';
  @override
  String get expiringSoon => 'Warning: Will Expire Soon';
  @override
  String get allProducts => 'All Products';
  @override
  String get quantity => 'Quantity';
  @override
  String get price => 'Price';
  @override
  String get expires => 'Expires';
  @override
  String get addProduct => 'Add Product';
  @override
  String get editProduct => 'Edit Product';
  @override
  String get categoryType => 'Category/Type';
  @override
  String get pricePerUnit => 'Price per Unit';
  @override
  String get purchasePrice => 'Purchase Price';
  @override
  String get expirationDate => 'Expiration Date';
  @override
  String get pleaseEnterCategory => 'Please enter a category';
  @override
  String get pleaseEnterQuantity => 'Please enter a quantity';
  @override
  String get pleaseEnterValidQuantity => 'Please enter a valid quantity';
  @override
  String get pleaseEnterPrice => 'Please enter a price';
  @override
  String get pleaseEnterValidPrice => 'Please enter a valid price';
  @override
  String get pleaseEnterPurchasePrice => 'Please enter a purchase price';
  @override
  String get pleaseEnterValidPurchasePrice =>
      'Please enter a valid purchase price';
  @override
  String get showAll => 'Show All';

  // Consumables
  @override
  String get consumables => 'Consumables';
  @override
  String get addConsumable => 'Add Consumable';
  @override
  String get editConsumable => 'Edit Consumable';
  @override
  String get deleteConsumable => 'Delete Consumable';
  @override
  String get consumableName => 'Consumable Name';
  @override
  String get consumablePrice => 'Consumable Price';
  @override
  String get totalConsumables => 'Total Consumables';
  @override
  String get pleaseEnterConsumableName => 'Please enter consumable name';
  @override
  String get pleaseEnterConsumablePrice => 'Please enter consumable price';
  @override
  String get pleaseEnterValidConsumablePrice =>
      'Please enter a valid consumable price';
  @override
  String get confirmDeleteConsumable =>
      'Are you sure you want to delete this consumable?';
  @override
  String get consumableDeletedSuccessfully => 'Consumable deleted successfully';
  @override
  String get consumableAddedSuccessfully => 'Consumable added successfully';
  @override
  String get consumableUpdatedSuccessfully => 'Consumable updated successfully';
  @override
  String get noConsumablesFound => 'No consumables found';
  @override
  String get addFirstConsumable => 'Add your first consumable';

  // Invoice Preview
  @override
  String get invoicePreview => 'Invoice Preview';
  @override
  String get editInvoice => 'Edit Invoice';
  @override
  String get removeProduct => 'Remove Product';
  @override
  String get addPayment => 'Add Payment';
  @override
  String get finalizeInvoice => 'Create Invoice';
  @override
  String get invoiceCreatedSuccessfully => 'Invoice created successfully';
  @override
  String get totalAmount => 'Total Amount';
  @override
  String get paidAmount => 'Paid Amount';
  @override
  String get collectedAmount => 'Collected Amount';
  @override
  String get pleaseEnterValidAmount => 'Please enter a valid amount';
  @override
  String get insufficientStock => 'Insufficient stock for';
  @override
  String get availableStock => 'Available stock';

  // Customers
  @override
  String get addCustomer => 'Add Customer';
  @override
  String get customerName => 'Customer Name';
  @override
  String get whatsappNumber => 'WhatsApp Number';
  @override
  String get pleaseEnterName => 'Please enter a name';
  @override
  String get pleaseEnterWhatsapp => 'Please enter a WhatsApp number';
  @override
  String get customerDetails => 'Customer Details';
  @override
  String get totalPaid => 'Total Paid';
  @override
  String get remainingBalance => 'Remaining Balance';
  @override
  String get paymentVoucher => 'Payment Voucher';

  // Invoices
  @override
  String get newInvoice => 'New Invoice';
  @override
  String get customer => 'Customer';
  @override
  String get selectCustomer => 'Please select a customer';
  @override
  String get selectedProducts => 'Selected Products';
  @override
  String get total => 'Total';
  @override
  String get createInvoice => 'Create Invoice';
  @override
  String get selectProducts => 'Select Products';
  @override
  String get noProductsAvailable => 'No products available';
  @override
  String get selectMultipleProducts =>
      'Select multiple products and set quantities';
  @override
  String get addSelected => 'Add Selected';
  @override
  String get invoices => 'Invoices';
  @override
  String get allInvoices => 'All Invoices';
  @override
  String get customerInvoices => 'Customer Invoices';
  @override
  String get date => 'Date';
  @override
  String get items => 'Items';
  @override
  String get product => 'Product';
  @override
  String get close => 'Close';
  @override
  String get share => 'Share';
  @override
  String get taxNumber => 'Tax Number';
  @override
  String get commercialRegistration => 'Commercial Registration';
  @override
  String get institutionName =>
      'Mohammed Ali Bakri Al-Zubaidi Veterinary Institution';

  // Buttons
  @override
  String get cancel => 'Cancel';
  @override
  String get save => 'Save';
  @override
  String get delete => 'Delete';
  @override
  String get add => 'Add';
  @override
  String get refreshData => 'Refresh Data';

  // Error messages
  @override
  String get errorAddingProduct => 'Error adding product';
  @override
  String get errorUpdatingProduct => 'Error updating product';
  @override
  String get errorDeletingProduct => 'Error deleting product';

  // Cashbox Page
  @override
  String get financialSummary => 'Financial Summary';
  @override
  String get lastUpdated => 'Last updated';
  @override
  String get sales => 'Sales';
  @override
  String get purchases => 'Purchases';
  @override
  String get totalProfit => 'Total Profit';
  @override
  String get recentTransactions => 'Recent Transactions';
  @override
  String get noRecentTransactions => 'No recent transactions';

  // Payment
  @override
  String get amount => 'Amount';
  @override
  String get pleaseEnterAmount => 'Please enter the amount';
  @override
  String get invalidAmount => 'Invalid amount';
  @override
  String get amountExceedsTotal => 'Amount cannot exceed total';
  @override
  String get pay => 'Pay';
  @override
  String get paymentSuccessful => 'Payment successful';

  // Search
  @override
  String get searchProducts => 'Search products...';
  @override
  String get searchResults => 'Search Results';

  // Returns/Refunds
  @override
  String get returnItems => 'Return Items';
  @override
  String get returnInvoice => 'Return Invoice';
  @override
  String get selectItemsToReturn => 'Select Items to Return';
  @override
  String get returnQuantity => 'Return Quantity';
  @override
  String get returnAmount => 'Return Amount';
  @override
  String get totalReturned => 'Total Returned';
  @override
  String get returnSuccessful => 'Return Successful';
  @override
  String get returnDialog => 'Return Dialog';
  @override
  String get confirmReturn => 'Confirm Return';
  @override
  String get returnNote => 'Return Note';
  @override
  String get originalQuantity => 'Original Quantity';
  @override
  String get returnedQuantity => 'Returned Quantity';
  @override
  String get netQuantity => 'Net Quantity';
  @override
  String get returnDate => 'Return Date';
  @override
  String get partialReturn => 'Partial Return';
  @override
  String get fullReturn => 'Full Return';
  @override
  String get cannotReturnMoreThanSold => 'Cannot return more than sold';
  @override
  String get pleaseSelectItemsToReturn => 'Please select items to return';
  @override
  String get originalAmount => 'Original Amount';
  @override
  String get netAmount => 'Net Amount';

  // Customer Summary
  @override
  String get customerSummary => 'Customer Summary';
  @override
  String get viewSummary => 'View Summary';
  @override
  String get totalInvoices => 'Total Invoices';
  @override
  String get totalInvoiceAmount => 'Total Invoice Amount';
  @override
  String get totalPaidByCustomer => 'Total Paid by Customer';
  @override
  String get totalRemainingAmount => 'Total Remaining Amount';
  @override
  String get invoiceNumber => 'Invoice Number';
  @override
  String get invoiceDate => 'Invoice Date';
  @override
  String get invoiceAmount => 'Invoice Amount';
  @override
  String get paidForInvoice => 'Paid for Invoice';
  @override
  String get remainingForInvoice => 'Remaining for Invoice';
  @override
  String get returnStatus => 'Return Status';
  @override
  String get noReturns => 'No Returns';
  @override
  String get hasReturns => 'Has Returns';

  // Collections
  @override
  String get collections => 'Collections';
  @override
  String get saveCollection => 'Save Collection';
  @override
  String get loadCollection => 'Load Collection';
  @override
  String get collectionName => 'Collection Name';
  @override
  String get enterCollectionName => 'Enter collection name';
  @override
  String get collectionSaved => 'Collection saved successfully';
  @override
  String get collectionLoaded => 'Collection loaded successfully';
  @override
  String get deleteCollection => 'Delete Collection';
  @override
  String get confirmDeleteCollection =>
      'Are you sure you want to delete this collection?';
  @override
  String get noCollections => 'No saved collections';
  @override
  String get manageCollections => 'Manage Collections';
  @override
  String get search => 'Search';
}
